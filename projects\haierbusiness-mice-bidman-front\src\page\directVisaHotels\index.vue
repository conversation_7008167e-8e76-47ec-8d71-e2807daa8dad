<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
  RadioGroup as hRadioGroup,
  RadioButton as hRadioButton,
  Table as hTable,
  Modal as hModal,
  Tree as hTree,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Switch as hSwitch,
  Select as hSelect,
  SelectOption as hSelectOption,
  Image as hImage
} from 'ant-design-vue';
import { onMounted, ref, computed, watch, createVNode } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { hotelApi, addressApi, directVisaHotelApi } from '@haierbusiness-front/apis';
import { GetMappingHotelListRes, GetHotelSyncRes, levelType, supplierType } from '@haierbusiness-front/common-libs'
import { DataType, usePagination, useRequest } from 'vue-request';
import { getCurrentRouter, errorModal, routerParam, getEnumOptions } from '@haierbusiness-front/utils';
import type { TreeProps } from 'ant-design-vue';

const store = applicationStore();
const { resource } = storeToRefs(store);
const tabValue = ref<string>("1")
const modalTabValue = ref<string>("1")
const mappingBoxShow = ref<boolean>(false)
const addBoxShow = ref<boolean>(false)
const showImage = ref<boolean>(true)
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const gotoDateils = (row: any) => {
  window.open(`${baseUrl}hbweb/hotel/#/hotel-analysis/hotelInfo?id=67328&code=${row.code}&date`, "_blank")
}
const levelTypeOptions = computed(() => {
  return getEnumOptions(levelType, true)
})

const columns = [
  {
    title: '图片',
    dataIndex: 'image',
    align: "center",
    width: "250px",
    key: 'image',
    ellipsis: true,
  },
  {
    title: '酒店名称',
    dataIndex: 'name',
    width: "250px",
    key: 'name',
  },
  {
    title: '酒店地址',
    dataIndex: 'address',
    width: "250px",
    key: 'address',
  },
  {
    title: '酒店电话',
    dataIndex: 'phone',
    align: "center",
    width: "250px",
    key: 'phone',
  },
  {
    title: '城市',
    dataIndex: 'cityName',
    align: "center",
    width: "120px",
    key: 'cityName',
    ellipsis: true,
  },
  {
    title: '区域',
    dataIndex: 'regionName',
    align: "center",
    width: "120px",
    key: 'regionName',
    ellipsis: true,
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    align: "center",
    width: "120px",
    key: 'brandName',
    ellipsis: true,
  },
  {
    title: '品牌集团',
    dataIndex: 'hotelBrandGroupName',
    align: "center",
    width: "120px",
    key: 'hotelBrandGroupName',
    ellipsis: true,
  }, {
    title: '星级',
    dataIndex: 'starLevel',
    align: "center",
    width: "120px",
    key: 'starLevel',
    ellipsis: true,
  },
  {
    title: '经纬度',
    dataIndex: 'lon',
    align: "center",
    width: "250px",
    ellipsis: true,
    key: 'lon',
  },
  {
    title: '编码',
    dataIndex: 'code',
    key: 'code',
    width: "220px",
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: "120px",
  },
  {
    title: '操作',
    dataIndex: '_operator',
    fixed: 'right',
    align: 'center',
    width: '200px',
  },
];

// 酒店分页
const searchParam = ref<any>({})
const {
  data: dataList,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(directVisaHotelApi.list);

const dataSource = computed(() => dataList.value?.records || []);

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    introduceMiceFlag:1,
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: dataList.value?.total,
  current: dataList.value?.pageNum,
  pageSize: dataList.value?.pageSize,
  style: { justifyContent: 'center' },
}));


// 供应商酒店分页
const searchSupplierParam = ref<any>({})
const {
  data: dataSupplierList,
  run: listSupplierApiRun,
  loading: supplierLoading,
  current: suppliercurrent,
  pageSize: supplierpageSize,
} = usePagination(hotelApi.getProvideHotelList);



// 映射弹窗标题
const boxTitle = ref<string>("")

// 酒店映射列表
const mappingHotelList = ref<GetMappingHotelListRes[]>([])

// 当前弹窗的国旅酒店code
const hotelCode = ref<string>("")


// 关联酒店

const toMappingMng = (row: any) => {
  boxTitle.value = row.name
  // 获取绑定的供应商酒店
  mappingBoxShow.value = true
  hotelCode.value = row.code
  getMappingHotelList(row.code)
}



const getStarLevelName = (starLevel: string) => {
  if (!starLevel) {
    return "";
  }
  const starLevelResult = levelTypeOptions.value.find((levelType) => levelType.value === starLevel);
  if (starLevelResult) {
    return starLevelResult.label;
  }
  return "";
}

// 请求国旅酒店映射
const mappingLoading = ref<boolean>(false)
const getMappingHotelList = (code: string) => {
  mappingLoading.value = true
  hotelApi.getMappingHotelList({ code: code }).then((res: any) => {
    mappingHotelList.value = res
    mappingLoading.value = false
  })
    .catch(() => {
      mappingLoading.value = false
    })
}


// 获取国内城市下拉列表
const hotelList = ref<any>([])
const getDistrictList = () => {
  addressApi.getDistrictList({ code: 'CN', level: 'city' }).then(res => {
    hotelList.value = res.records
  })
}
const filterOption = (input: string, option: any) => {
  console.log(input, option)
  if (option.name) {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  } else {
    return option.brandName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
};

// 获取区域
const areaList = ref<any>([])
const getAreaList = (value: number) => {
  addressApi.getDistrictList({ code: 'CN', level: 'district', cityId: value }).then(res => {
    areaList.value = res.records
  })
}

// 选择完城市 获取区域
const cityChange = (value: number) => {
  console.log(value)
  if (value) {
    getAreaList(value)
  } else {
    areaList.value = []
  }
  searchSupplierParam.value.mappingRegionId=null;
  searchParam.value.regionId=null
}

// 获取品牌下拉列表
const BrandList = ref<any>([])
const getBrandList = () => {
  hotelApi.getHotelBrandProviderMapList({ type: 2 }).then(res => {
    BrandList.value = res.records
  })
}

// 获取集团下拉列表 BrandGroupList
const BrandGroupList = ref<any>([])
const getBrandGroupList = () => {
  hotelApi.getHotelBrandProviderMapList({ type: 1 }).then(res => {
    BrandGroupList.value = res.records
  })
}
const clear = (type: number) => {
  if (type == 1) {
    searchParam.value = {}
    listApiRun({
      introduceMiceFlag:1,
      ...searchParam.value,
      pageNum: current.value,
      pageSize: pageSize.value,
    });
  } else {
    searchSupplierParam.value = {}
    listSupplierApiRun({
      ...searchParam.value,
      pageNum: suppliercurrent.value,
      pageSize: supplierpageSize.value,
    });
  }
}

const openIdList = ref<string[]>([])

const open = (row: any, type: number) => {
  if (type == 1) {
    openIdList.value.push(row.code)
  } else {
    openIdList.value = openIdList.value.filter(item => item != row.code)
  }
}

onMounted(() => {
  listApiRun({
    introduceMiceFlag:1,
    ...searchParam.value,
    pageNum: 1,
    pageSize: 10,
  });
  getDistrictList()
  getBrandList()
  getBrandGroupList()
})

</script>

<template>
  <div class="pageBox">
    <div class="headerBox">
      <h-form :labelCol="{ span: 5, offset: 1 }" style="width:100%;">
        <h-row>
          <h-col :span="6">
            <h-form-item label="名称">
              <h-input allow-clear v-model:value="searchParam.name" placeholder="名称" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="城市">
              <h-select ref="city" @change="cityChange" show-search :fieldNames="{ label: 'name', value: 'id' }"
                :options="hotelList" :filter-option="filterOption" v-model:value="searchParam.cityId" style="width: 100%"
                allow-clear placeholder="城市" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="区域">
              <h-select ref="city" :disabled="!searchParam.cityId" show-search
                :fieldNames="{ label: 'name', value: 'id' }" :options="areaList" placeholder="区域"
                :filter-option="filterOption" v-model:value="searchParam.regionId" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="地址">
              <h-input allow-clear v-model:value="searchParam.address" placeholder="地址" />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="编码">
              <h-input allow-clear v-model:value="searchParam.code" placeholder="编码" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="电话">
              <h-input allow-clear v-model:value="searchParam.phone" placeholder="电话" />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="品牌">
              <h-select ref="city" show-search :fieldNames="{ label: 'brandName', value: 'id' }" :options="BrandList"
                :filter-option="filterOption" placeholder="品牌" mode="multiple" :maxTagCount="1"
                v-model:value="searchParam.brandIds" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="集团">
              <h-select ref="city" show-search :fieldNames="{ label: 'brandName', value: 'id' }" :options="BrandGroupList"
                :filter-option="filterOption" mode="multiple" :maxTagCount="1" v-model:value="searchParam.brandGroupIds"
                placeholder="集团" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row>
          <h-col :span="6">
            <h-form-item label="星级">
              <h-select ref="city" show-search :options="levelTypeOptions" mode="multiple" :maxTagCount="1"
                v-model:value="searchParam.starLevels" placeholder="星级" style="width: 100%" allow-clear />
            </h-form-item>
          </h-col>
          <!-- <h-col :span="6">
            <h-form-item label="经度">
              <h-input
                allow-clear
                v-model:value="searchParam.lon"
                placeholder="经度"
              />
            </h-form-item>
          </h-col>
          <h-col :span="6">
            <h-form-item label="纬度">
              <h-input allow-clear v-model:value="searchParam.lat" placeholder="纬度" />
            </h-form-item>
          </h-col> -->
        </h-row>
        <h-row>
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right:10px;" @click="clear(1)">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-form>
    </div>
    <div class="contentBox">
      <!--         :row-selection="rowSelection" -->
      <h-table :columns="columns" :size="'small'" :scroll="{ x: 1550 }"
        :data-source="dataSource" :loading="loading" :pagination="pagination" @change="handleTableChange($event as any)">
        <template #headerCell="{ column }">
          <template v-if="column.key === 'image'">
            <span>
              图片
            </span>
            <a-switch style="margin-left:10px;" v-model:checked="showImage" />
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'image'">
            <h-image v-if="showImage" :width="200" :height="100" :src="record.image"
              fallback="data:image/png;base64,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" />
          </template>
          <template v-if="column.dataIndex === 'phone'">
            <p style="word-break: break-all;">{{ record.phone }}</p>
          </template>
                  <!-- 状态 -->
             <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="record.status == 1" color="green">上架</a-tag>
            <a-tag v-if="record.status == 0" color="red">下架</a-tag>
          </template>
          <template v-if="column.dataIndex === 'starLevel'">
            <p style="word-break: break-all;">{{ getStarLevelName(record.starLevel) }}</p>
          </template>
          <template v-if="column.dataIndex === 'lon'">
            <div v-if="record.gdLon">经度：{{ record.gdLon }} <br> 纬度：{{ record.gdLat }}</div>
            <div v-else-if="record.gLon">经度：{{ record.gLon }} <br> 纬度：{{ record.gLat }}</div>
            <div v-else-if="record.bdLon">经度：{{ record.bdLon }} <br> 纬度：{{ record.bdLat }}</div>
          </template>
          <template v-if="column.dataIndex === 'name'">
            <div :title="record.name" class="multi-line-ellipsis">
              {{ record.name }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'address'">
            <div :title="record.address" class="multi-line-ellipsis">
              {{ record.address }}
            </div>
          </template>
          <!-- 映射mappingHotelList -->
          <template v-if="column.dataIndex === 'mappingHotelList'">
            <div v-if="record.mappingHotelList">
              <p style="width:250px;word-break:break-all;white-space: break-spaces;"
                v-for="(item, index) in record.mappingHotelList"
                v-show="openIdList.includes(record.code) ? true : index == 0">{{
                  item.providerCodeName }}:{{ item.name }}</p>
              <a v-if="record.mappingHotelList.length > 1 && !openIdList.includes(record.code)"
                @click="open(record, 1)">展开</a>
              <a v-if="record.mappingHotelList.length > 1 && openIdList.includes(record.code)"
                @click="open(record, 2)">收起</a>
            </div>
          </template>
          <template v-if="column.dataIndex === '_operator'">
            <h-button @click="gotoDateils(record)" type="link">详情</h-button>
          </template>
        </template>
      </h-table>
    </div>
  </div>
</template>
<style scoped lang="less">
.pageBox {
  padding: 0 8px;
  display: flex;
  flex-flow: column;

  // height: 100vh;
  .headerBox {
    width: 100%;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 12px;
  }

  .inputBox {
    display: flex;
    align-items: center;
    // margin-left: 60px;
  }

  .contentBox {
    margin-top: 16px;
    background: #fff;
    // flex: 1;
  }
}

.modalHeaderBox {
  width: 100%;
  // display: flex;
}

.ant-form-item {
  margin-bottom: 16px;
}</style>
