<script setup lang="ts">
// 方案变更-日程安排
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, computed, nextTick, defineProps, defineEmits, defineExpose } from 'vue';

import { errorModal, resolveParam, routerParam } from '@haierbusiness-front/utils';
import {
  DemandSubmitObj,
  schemeStaysArr,
  schemePlacesArr,
  schemeCateringsArr,
  schemeVehiclesArr,
  schemeAttendantsArr,
  schemeActivitiesArr,
  schemeInsurancesArr,
} from '@haierbusiness-front/common-libs';

import dayjs, { Dayjs } from 'dayjs';

import changeStay from './changeStay.vue';
import changePlace from './changePlace.vue';
import changeCatering from './changeCaterings.vue';
import changeVehicle from './changeVehicles.vue';
import changeAttendant from './changeAttendant.vue';
import changeActivity from './changeActivity.vue';
import changeInsurance from './changeInsurance.vue';

const props = defineProps({
  schemeContainerRef: {}, // 父级
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  userType: {
    // platform,user,manage
    type: String,
    default: 'platform',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  hotelList: {
    type: Array,
    default: [],
  },
  merchantType: {
    type: Number,
    default: null,
  },
  processNode: {
    type: String,
    default: '',
  },
  isCateringStandardControl: {
    // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
    type: String,
    default: '',
  },
  meetingMinDaysNum: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['planPriceEmit', 'planEachPriceEmit', 'schemePlanEmit', 'schemeChangePlanDateEmit']);

const schemeChangeLoadingEnd = ref<Boolean>(false);

const changeDateShow = ref<Boolean>(false);
const changeBeginDate = ref<string>('');
const changeEndDate = ref<string>('');

const schemeStays = ref<array>([]);
const schemeDifferenceStays = ref<array>([]);
const schemePlaces = ref<array>([]);
const schemeCaterings = ref<array>([]);
const schemeVehicles = ref<array>([]);
const schemeAttendants = ref<array>([]);
const schemeActivities = ref<array>([]);
const schemeInsurances = ref<array>([]);

const stayRef = ref(null);
const placeRef = ref(null);
const cateringRef = ref(null);
const vehicleRef = ref(null);
const attendantRef = ref(null);
const activityRef = ref(null);
const insuranceRef = ref(null);

const schemePlanList = ref<array>([]); // 方案计划列表
const cacheSchemePlanList = ref<array>([]); // 方案计划列表
const dateRangeList = ref<array>([]); // 所有日期列表

const stayPriceArr = ref<array>([]); // 住宿 - 方案金额
const placePriceArr = ref<array>([]); // 会场 - 方案金额
const cateringPriceArr = ref<array>([]); // 用餐 - 方案金额
const vehiclePriceArr = ref<array>([]); // 用车 - 方案金额
const attendantPriceArr = ref<array>([]); // 服务人员 - 方案金额
const activityPriceArr = ref<array>([]); // 拓展活动 - 方案金额
const insurancePriceArr = ref<array>([]); // 保险 - 方案金额

// 时间选择
const changeDate = (startDate: string, endDate: string) => {
  dateRangeList.value = [];

  if (startDate && endDate) {
    // 相差天数
    const returnInt = calculateDateDifference(startDate, endDate);

    for (let index = 0; index <= returnInt; index++) {
      // 日期+n
      dateRangeList.value.push(getNextDay(startDate, index));
    }
  }
};
// 相差天数
const calculateDateDifference = (date1: string, date2: string) => {
  // 将日期转换为Date对象
  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // 计算毫秒差值
  const diffTime = Math.abs(d2.getTime() - d1.getTime());

  // 将毫秒转换为天数
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};
// 日期加N天
const getNextDay = (date: string, num: number) => {
  // 创建一个新的 Date 对象
  var nextDay = new Date(date);
  // 获取当前日期的天数并加n
  nextDay.setDate(nextDay.getDate() + num);
  // 返回加一天后的日期字符串
  return nextDay.toISOString().split('T')[0];
  // return nextDay.toISOString().split('T')[0].replace(/-/g, '/');
};

// 住宿
const schemeStaysEmit = (staysArr: schemeStaysArr) => {
  schemeStays.value[staysArr.schemeIndex] = [...staysArr.schemeStays];
  schemeDifferenceStays.value[staysArr.schemeIndex] = [...staysArr.schemeDifferenceStays];
};

// 会场
const schemePlacesEmit = (placesArr: schemePlacesArr) => {
  schemePlaces.value[placesArr.schemeIndex] = [...placesArr.schemePlaces];
};

// 用餐
const schemeCateringsEmit = (cateringsArr: schemeCateringsArr) => {
  schemeCaterings.value[cateringsArr.schemeIndex] = [...cateringsArr.schemeCaterings];
};

// 用车
const schemeVehiclesEmit = (vehiclesArr: schemeVehiclesArr) => {
  schemeVehicles.value[vehiclesArr.schemeIndex] = [...vehiclesArr.schemeVehicles];
};

// 服务人员
const schemeAttendantsEmit = (attendantsArr: schemeAttendantsArr) => {
  schemeAttendants.value[attendantsArr.schemeIndex] = [...attendantsArr.schemeAttendants];
};

// 拓展活动
const schemeActivityEmit = (activitiesArr: schemeActivitiesArr) => {
  schemeActivities.value[activitiesArr.schemeIndex] = [...activitiesArr.schemeActivities];
};

// 保险
const schemeInsurancesEmit = (insurancesArr: schemeInsurancesArr) => {
  schemeInsurances.value[insurancesArr.schemeIndex] = [...insurancesArr.schemeInsurances];
};

// 价格
const schemePriceEmit = (priceObj) => {
  switch (priceObj.type) {
    case 'stay':
      // 住宿
      stayPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;
      // console.log('%c [ 住宿 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', stayPriceArr.value);
      break;

    case 'place':
      // 会场
      placePriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 会场 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', placePriceArr.value);
      break;

    case 'catering':
      // 用餐
      cateringPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 用餐 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', cateringPriceArr.value);
      break;

    case 'vehicle':
      // 用车
      vehiclePriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 用车 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', vehiclePriceArr.value);
      break;

    case 'attendant':
      // 服务人员
      attendantPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 服务 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', attendantPriceArr.value);
      break;

    case 'activity':
      // 拓展活动
      activityPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 活动 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', activityPriceArr.value);
      break;

    case 'insurance':
      // 保险
      insurancePriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 保险 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', insurancePriceArr.value);
      break;

    default:
      break;
  }

  const planPrice =
    addSum(stayPriceArr.value) +
    addSum(placePriceArr.value) +
    addSum(cateringPriceArr.value) +
    addSum(vehiclePriceArr.value) +
    addSum(attendantPriceArr.value) +
    addSum(activityPriceArr.value) +
    addSum(insurancePriceArr.value);

  emit('planPriceEmit', planPrice);
  emit('planEachPriceEmit', [
    { type: 1, label: '住宿', price: addSum(stayPriceArr.value) },
    { type: 2, label: '会场', price: addSum(placePriceArr.value) },
    { type: 4, label: '用餐', price: addSum(cateringPriceArr.value) },
    { type: 8, label: '用车', price: addSum(vehiclePriceArr.value) },
    { type: 16, label: '服务人员', price: addSum(attendantPriceArr.value) },
    { type: 32, label: '拓展活动', price: addSum(activityPriceArr.value) },
    { type: 64, label: '保险', price: addSum(insurancePriceArr.value) },
  ]);
};

function addSum(arr) {
  let sum = 0;
  arr.forEach((element) => {
    sum += element;
  });
  return sum;
}

// 不可选日期
const disabledDate = (current: Dayjs) => {
  // 当前日期之前的日期都禁用
  return (
    current &&
    current <
      dayjs()
        .add(props.meetingMinDaysNum - 1, 'day')
        .endOf('day')
  );
};

// 方案变更
const changePlanDate = (date: string, index: number) => {
  if (index > 0) {
    return;
  }
  changeBeginDate.value = '';
  changeEndDate.value = '';

  changeDateShow.value = true;
};
const changeDateConfirm = async () => {
  if (!changeBeginDate.value) {
    message.error('请选择变更开始日期！');
    return;
  }

  // 相差天数
  const dateLength = dateRangeList.value.length;

  // 结束日期
  changeEndDate.value = getNextDay(changeBeginDate.value, dateLength - 1);

  await changeDate(changeBeginDate.value, changeEndDate.value);

  emit('schemeChangePlanDateEmit', {
    startDate: changeBeginDate.value,
    endDate: changeEndDate.value,
  });

  closeModal();
};
const closeModal = () => {
  changeDateShow.value = false;
};

// 每日计划 - 暂存
const schemePlanTempSave = () => {
  // 住宿
  if (stayRef.value) {
    stayRef.value.forEach((e) => {
      e.stayTempSave();
    });
  }

  // 会场
  if (placeRef.value) {
    placeRef.value.forEach((e) => {
      e.placeTempSave();
    });
  }

  // 用餐
  if (cateringRef.value) {
    cateringRef.value.forEach((e) => {
      e.cateringTempSave();
    });
  }

  // 用车
  if (vehicleRef.value) {
    vehicleRef.value.forEach((e) => {
      e.vehicleTempSave();
    });
  }

  // 服务人员
  if (attendantRef.value) {
    attendantRef.value.forEach((e) => {
      e.attendantTempSave();
    });
  }

  // 拓展方案
  if (activityRef.value) {
    activityRef.value.forEach((e) => {
      e.activityTempSave();
    });
  }

  // 保险方案
  if (insuranceRef.value) {
    insuranceRef.value.forEach((e) => {
      e.insuranceTempSave();
    });
  }

  let stays = [];
  let differenceStays = [];
  let places = [];
  let caterings = [];
  let vehicles = [];
  let attendants = [];
  let activities = [];
  let insurances = [];

  schemeStays.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    stays = stays.concat(e);
  });

  schemeDifferenceStays.value.forEach((e, i) => {
    e.forEach((j) => {
      j.differenceDate = dateRangeList.value[i];
    });

    differenceStays = differenceStays.concat(e);
  });

  schemePlaces.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    places = places.concat(e);
  });
  schemeCaterings.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    caterings = caterings.concat(e);
  });
  schemeVehicles.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    vehicles = vehicles.concat(e);
  });
  schemeAttendants.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    attendants = attendants.concat(e);
  });
  schemeActivities.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    activities = activities.concat(e);
  });
  schemeInsurances.value.forEach((e, i) => {
    e.forEach((j) => {
      j.demandDate = dateRangeList.value[i];
    });

    insurances = insurances.concat(e);
  });

  emit('schemePlanEmit', {
    stays: [...stays],
    differenceStays: [...differenceStays],
    places: [...places],
    caterings: [...caterings],
    vehicles: [...vehicles],
    attendants: [...attendants],
    activities: [...activities],
    insurances: [...insurances],
  });
};

// 每日计划 - 校验
const SchemePlanSub = () => {
  let verifyStays = true; // 住宿
  let verifyPlaces = true; // 会场
  let verifyCaterings = true; // 用餐
  let verifyVehicles = true; // 用车
  let verifyAttendants = true; // 服务人员
  let verifyActivities = true; // 拓展活动
  let verifyInsurances = true; // 保险
  let verifyTraffic = true; // 交通

  // 住宿
  if (stayRef.value) {
    verifyStays = stayRef.value.every((e) => e.staySub());
  }
  if (!verifyStays) {
    return false;
  }

  // 会场
  if (placeRef.value) {
    verifyPlaces = placeRef.value.every((e) => e.placeSub());
  }
  if (!verifyPlaces) {
    return false;
  }

  // 用餐
  if (cateringRef.value) {
    verifyCaterings = cateringRef.value.every((e) => e.cateringSub());
  }
  if (!verifyCaterings) {
    return false;
  }

  // 用车
  if (vehicleRef.value) {
    verifyVehicles = vehicleRef.value.every((e) => e.vehicleSub());
  }
  if (!verifyVehicles) {
    return false;
  }

  // 服务人员
  if (attendantRef.value) {
    verifyAttendants = attendantRef.value.every((e) => e.attendantSub());
  }
  if (!verifyAttendants) {
    return false;
  }

  // 拓展方案
  if (activityRef.value) {
    verifyActivities = activityRef.value.every((e) => e.activitySub());
  }
  if (!verifyActivities) {
    return false;
  }

  // 保险方案
  if (insuranceRef.value) {
    verifyInsurances = insuranceRef.value.every((e) => e.insuranceSub());
  }
  if (!verifyInsurances) {
    return false;
  }

  // 是否校验通过
  const isAllVer =
    verifyStays &&
    verifyPlaces &&
    verifyCaterings &&
    verifyVehicles &&
    verifyAttendants &&
    verifyActivities &&
    verifyInsurances;
  // TODO - 交通
  // && verifyTraffic

  if (isAllVer) {
    let stays = [];
    let differenceStays = [];
    let places = [];
    let caterings = [];
    let vehicles = [];
    let attendants = [];
    let activities = [];
    let insurances = [];

    schemeStays.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      stays = stays.concat(e);
    });
    schemeDifferenceStays.value.forEach((e, i) => {
      e.forEach((j) => {
        j.differenceDate = dateRangeList.value[i];
      });

      differenceStays = differenceStays.concat(e);
    });

    schemePlaces.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      places = places.concat(e);
    });
    schemeCaterings.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      caterings = caterings.concat(e);
    });
    schemeVehicles.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      vehicles = vehicles.concat(e);
    });
    schemeAttendants.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      attendants = attendants.concat(e);
    });
    schemeActivities.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      activities = activities.concat(e);
    });
    schemeInsurances.value.forEach((e, i) => {
      e.forEach((j) => {
        j.demandDate = dateRangeList.value[i];
      });

      insurances = insurances.concat(e);
    });

    emit('schemePlanEmit', {
      stays: [...stays],
      differenceStays: [...differenceStays],
      places: [...places],
      caterings: [...caterings],
      vehicles: [...vehicles],
      attendants: [...attendants],
      activities: [...activities],
      insurances: [...insurances],
    });
  }

  return isAllVer;
};

defineExpose({ SchemePlanSub, schemePlanTempSave });

onMounted(async () => {
  schemeChangeLoadingEnd.value = false;
  // 方案详情
  const dataInfo = props.schemeInfo;

  if (dataInfo.startDate && dataInfo.endDate) {
    // 日期反显
    await changeDate(dataInfo.startDate, dataInfo.endDate);

    // 日程安排反显
    schemePlanList.value = [];

    // 日程安排反显
    dateRangeList.value.forEach((e, i) => {
      let stays = (dataInfo.stays && dataInfo.stays.filter((e1) => e === e1.demandDate)) || [];
      let places = (dataInfo.places && dataInfo.places.filter((e1) => e === e1.demandDate)) || [];
      let caterings = (dataInfo.caterings && dataInfo.caterings.filter((e1) => e === e1.demandDate)) || [];
      let vehicles = (dataInfo.vehicles && dataInfo.vehicles.filter((e1) => e === e1.demandDate)) || [];
      let attendants = (dataInfo.attendants && dataInfo.attendants.filter((e1) => e === e1.demandDate)) || [];
      let activities = (dataInfo.activities && dataInfo.activities.filter((e1) => e === e1.demandDate)) || [];
      let insurances = (dataInfo.insurances && dataInfo.insurances.filter((e1) => e === e1.demandDate)) || [];
      let differenceStays =
        (dataInfo.differenceStays && dataInfo.differenceStays.filter((e1) => e === e1.differenceDate)) ||
        (dataInfo.differences && dataInfo.differences.filter((e1) => e === e1.differenceDate)) ||
        [];

      // // 用餐
      // caterings.forEach((e) => {
      //   e.isInsideHotel = e.isInsideHotel ? 1 : 0;
      // });

      // // 用车
      // vehicles.forEach((e) => {
      //   if (e.route) {
      //     // 路线反显
      //     e.routeList = e.route.split(',');
      //   }
      // });

      // // 拓展活动-上传资料-反显
      // activities.forEach((j) => {
      //   if (j.paths && j.paths.length > 0) {
      //     j.fileList = [];
      //     j.paths.forEach((g) => {
      //       let gObj = {};
      //       let isJson = true;
      //       try {
      //         gObj = JSON.parse(g);
      //       } catch (error) {
      //         isJson = false;
      //       }

      //       if (!isJson) return;

      //       j.fileList.push({ name: gObj.name, filePath: gObj.url });
      //     });
      //   }
      // });

      schemePlanList.value.push({
        stays: stays,
        places: places,
        caterings: caterings,
        vehicles: vehicles,
        attendants: attendants,
        activities: activities,
        insurances: insurances,
        differenceStays: differenceStays,
        demandDate: e,
        key: Date.now() + i,
      });
    });
  }

  if (props.isSchemeCache) {
    // 方案缓存
    const dataCacheInfo = props.schemeCacheInfo;

    if (dataCacheInfo.startDate && dataCacheInfo.endDate) {
      // 日期反显
      await changeDate(dataCacheInfo.startDate, dataCacheInfo.endDate);

      // 日程安排反显
      cacheSchemePlanList.value = [];

      // 日程安排反显
      dateRangeList.value.forEach((e, i) => {
        let stays = (dataCacheInfo.stays && dataCacheInfo.stays.filter((e1) => e === e1.demandDate)) || [];
        let places = (dataCacheInfo.places && dataCacheInfo.places.filter((e1) => e === e1.demandDate)) || [];
        let caterings = (dataCacheInfo.caterings && dataCacheInfo.caterings.filter((e1) => e === e1.demandDate)) || [];
        let vehicles = (dataCacheInfo.vehicles && dataCacheInfo.vehicles.filter((e1) => e === e1.demandDate)) || [];
        let attendants =
          (dataCacheInfo.attendants && dataCacheInfo.attendants.filter((e1) => e === e1.demandDate)) || [];
        let activities =
          (dataCacheInfo.activities && dataCacheInfo.activities.filter((e1) => e === e1.demandDate)) || [];
        let insurances =
          (dataCacheInfo.insurances && dataCacheInfo.insurances.filter((e1) => e === e1.demandDate)) || [];
        let differenceStays =
          (dataCacheInfo.differenceStays && dataCacheInfo.differenceStays.filter((e1) => e === e1.differenceDate)) ||
          (dataCacheInfo.differences && dataCacheInfo.differences.filter((e1) => e === e1.differenceDate)) ||
          [];

        // // 用餐
        // caterings.forEach((e) => {
        //   e.isInsideHotel = e.isInsideHotel ? 1 : 0;
        // });

        // // 用车
        // vehicles.forEach((e) => {
        //   if (e.route) {
        //     // 路线反显
        //     e.routeList = e.route.split(',');
        //   }
        // });

        // // 拓展活动-上传资料-反显
        // activities.forEach((j) => {
        //   if (j.paths && j.paths.length > 0) {
        //     j.fileList = [];
        //     j.paths.forEach((g) => {
        //       let gObj = {};
        //       let isJson = true;
        //       try {
        //         gObj = JSON.parse(g);
        //       } catch (error) {
        //         isJson = false;
        //       }

        //       if (!isJson) return;

        //       j.fileList.push({ name: gObj.name, filePath: gObj.url });
        //     });
        //   }
        // });

        cacheSchemePlanList.value.push({
          stays: stays,
          places: places,
          caterings: caterings,
          vehicles: vehicles,
          attendants: attendants,
          activities: activities,
          insurances: insurances,
          differenceStays: differenceStays,
          demandDate: e,
          key: Date.now() + i,
        });
      });
    }

    if (cacheSchemePlanList.value.length > schemePlanList.value.length) {
      // 若缓存比方案详情数量多，说明新增日期
      cacheSchemePlanList.value.forEach((e, idx) => {
        if (idx >= schemePlanList.value.length) {
          schemePlanList.value[idx] = {
            stays: [],
            places: [],
            caterings: [],
            vehicles: [],
            attendants: [],
            activities: [],
            insurances: [],
            differenceStays: [],
            demandDate: e.demandDate,
            key: Date.now() + idx,
          };
        }
      });
    }
  }

  schemeChangeLoadingEnd.value = true;
});
</script>

<template>
  <!-- 方案互动-日程安排 -->
  <div class="interact_schedule_plan">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>日程安排</span>
    </div>

    <div v-for="(item, idx) in schemePlanList" :key="item.demandDate">
      <a-affix :offset-top="20" :target="() => schemeContainerRef">
        <div
          :class="['date_title', 'mt20', 'mb20', idx === 0 ? 'date_title_cursor' : '']"
          @click="changePlanDate(item.demandDate, idx)"
        >
          {{ dateRangeList[idx] }}
        </div>
      </a-affix>

      <!-- 住宿 -->
      <change-stay
        v-if="schemeChangeLoadingEnd && ([1, 2].includes(merchantType) || userType === 'user')"
        ref="stayRef"
        class="interact_stay"
        :processNode="processNode"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        :hotels="hotelList"
        :merchantType="merchantType"
        @schemePriceEmit="schemePriceEmit"
        @schemeStaysEmit="schemeStaysEmit"
      />
      <!-- 会场 -->
      <change-place
        v-if="schemeChangeLoadingEnd && ([1, 2].includes(merchantType) || userType === 'user')"
        ref="placeRef"
        class="interact_place"
        :processNode="processNode"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        :hotels="hotelList"
        :merchantType="merchantType"
        @schemePriceEmit="schemePriceEmit"
        @schemePlacesEmit="schemePlacesEmit"
      />
      <!-- 用餐 -->
      <change-catering
        v-if="schemeChangeLoadingEnd && ([1, 2].includes(merchantType) || userType === 'user')"
        ref="cateringRef"
        class="interact_can"
        :processNode="processNode"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        :hotels="hotelList"
        :merchantType="merchantType"
        :isCateringStandardControl="isCateringStandardControl"
        @schemePriceEmit="schemePriceEmit"
        @schemeCateringsEmit="schemeCateringsEmit"
      />
      <!-- 用车 -->
      <change-vehicle
        v-if="schemeChangeLoadingEnd && ([1, 2, 5].includes(merchantType) || userType === 'user')"
        ref="vehicleRef"
        class="interact_che"
        :processNode="processNode"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        @schemePriceEmit="schemePriceEmit"
        @schemeVehiclesEmit="schemeVehiclesEmit"
      />
      <!-- 服务人员 -->
      <change-attendant
        v-if="schemeChangeLoadingEnd && ([1, 2].includes(merchantType) || userType === 'user')"
        ref="attendantRef"
        class="interact_service"
        :processNode="processNode"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        @schemePriceEmit="schemePriceEmit"
        @schemeAttendantsEmit="schemeAttendantsEmit"
      />
      <!-- 拓展活动 -->
      <change-activity
        v-if="schemeChangeLoadingEnd && ([1, 2].includes(merchantType) || userType === 'user')"
        ref="activityRef"
        class="interact_activity"
        :processNode="processNode"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        @schemePriceEmit="schemePriceEmit"
        @schemeActivityEmit="schemeActivityEmit"
      />
      <!-- 保险 -->
      <!-- <change-insurance
        v-if="schemeChangeLoadingEnd && (merchantType === 3 || userType === 'user')"
        ref="insuranceRef"
        class="interact_ins"
        :schemeChangeType="schemeChangeType"
        :schemeItem="item"
        :schemeCacheItem="cacheSchemePlanList[idx]"
        :schemeIndex="idx"
        :isSchemeCache="isSchemeCache"
        @schemePriceEmit="schemePriceEmit"
        @schemeInsurancesEmit="schemeInsurancesEmit"
      /> -->
    </div>

    <!-- 日期变更 - 弹窗 -->
    <a-modal
      title="日期变更"
      :open="changeDateShow"
      @ok="changeDateConfirm"
      @cancel="closeModal"
      width="500px"
      destroyOnClose
    >
      <div class="scheme_change_date_list">
        <div class="scheme_change_date_label">变更开始日期：</div>
        <div class="scheme_change_date_value">
          <a-date-picker
            v-model:value="changeBeginDate"
            :disabledDate="disabledDate"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.interact_schedule_plan {
  .date_title {
    display: inline-block;
    padding: 4px 16px;

    font-size: 14px;
    color: #ffffff;

    background: #1868db;
    border-radius: 4px;
  }
  .date_title_cursor {
    cursor: pointer;

    &:hover {
      background: rgba(24, 104, 219, 0.7);
    }
  }

  .interact_stay {
  }
  .interact_place {
  }
  .interact_can {
  }
  .interact_che {
  }
  .interact_service {
  }
  .interact_activity {
  }
  .interact_ins {
  }
}

:deep(.ant-affix) {
  width: auto !important;
}
.scheme_change_date_list {
  display: flex;
  align-items: center;
  justify-content: start;
}
</style>
