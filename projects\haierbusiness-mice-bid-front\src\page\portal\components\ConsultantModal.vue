<script setup lang="ts">
import {computed, ref} from 'vue';
import dayjs from 'dayjs';
import CalendarImg from '@/assets/image/icon/Calendar.png';
import CheckCircleFilledImg from '@/assets/image/icon/CheckCircleFilled.png';
import {usePortalStore} from '../store';
import {message} from 'ant-design-vue';
import {useRoute, useRouter} from 'vue-router';
import {routerParam, saveDataBy} from '@haierbusiness-front/utils';

import {storeToRefs} from 'pinia';
import {applicationStore} from '@haierbusiness-front/utils/src/store/applicaiton';

const {loginUser} = storeToRefs(applicationStore());

const route = useRoute();

interface MeetingParams {
  pdMainId: number;
  miceName: string;
  startDate: string;
  endDate: string;
  demandType: number;
  districtType?: number;
  personTotal: number | null;
  miceType?: number;
  intentionConsultantUserCode?: string;
}

interface Props {
  modelValue: boolean;
  processId: string;
  meetingMinDaysNum: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  processId: '',
});
const emit = defineEmits(['update:modelValue']);

const loading = ref(false);

const router = useRouter();
const store = usePortalStore();
const consultantType = ref<'0' | '1'>('0');
const consultantId = ref<string>('');

const modelOpen = computed({
  get: () => props.modelValue,
  set: (v) => emit('update:modelValue', v),
});

const adviserOpen = ref<boolean>(false);

const validate = () => {
  let flag = true;
  if (!store.meetingInfo.meetingDate?.length) {
    message.error('会议日期不能为空');
    flag = false;
  }
  return flag;
};

const next = async () => {
  const validateRes = validate();
  if (validateRes) {
    const params: MeetingParams = {
      pdMainId: props.processId ? parseInt(props.processId) : 87, // TODO 流程id
      miceName: store.meetingInfo.miceName,
      personTotal: store.meetingInfo.personTotal,
      startDate: store.meetingInfo.meetingDate[0]?.format('YYYY-MM-DD') ?? '',
      endDate: store.meetingInfo.meetingDate[1]?.format('YYYY-MM-DD') ?? '',
      demandType: store.meetingInfo.demandType ? 4 : 1, // 1, "用户提报版本"   4, "顾问代提版本"
      districtType: store.meetingInfo.districtType ?? 0,
      miceType: store.meetingInfo.miceType ?? 0,
    };

    if (consultantType.value === '1' && consultantId.value === '') {
      message.error('请选择意向顾问！');
      return;
    }

    loading.value = true;

    if (consultantType.value === '1') {
      params.intentionConsultantUserCode = consultantId.value;
    }

    const res = await store.createOrder(params);
    if (res?.success && res?.data.mainId) {
      const meetingCreate = {
        contactUserName: loginUser.value?.nickName || '', // 会议对接人姓名
        contactUserCode: loginUser.value?.username || '', // 会议对接人工号
        contactUserPhone: loginUser.value?.phone || '', // 会议对接人手机号
        contactUserEmail: loginUser.value?.email || '', // 会议对接人邮箱
      };

      const record = {
        miceId: res?.data.mainId,
        mainCode: res?.data.mainCode || '',

        pdMainId: route.query.processId || route.query.pdMainId,
      };

      // 后端缓存
      await saveDataBy({
        applicationCode: 'haierbusiness-mice-bid',
        // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
        cacheKey: 'haierbusiness-mice-bid_' + loginUser.value?.username + '_demandSubKey' + record.miceId, // 需求提报
        cacheValue: JSON.stringify({
          ...params,
          ...record,
          ...meetingCreate,
          hotels: [],
        }),
      });

      if (store.meetingInfo.demandType) {
        // 顾问代提
        // message.success('提交成功！会议顾问稍候与您联系！');

        // 顾问代提 - 弹窗
        adviserOpen.value = true;
      } else {
        // 需求提报
        router.push({path: '/demand/index', query: {record: routerParam(record)}});
      }
    }

    modelOpen.value = false;
    loading.value = false;
  }
};

const handleOk = () => {
  adviserOpen.value = false;

  // 清空选项
  store.meetingInfo = {
    miceName: '',
    meetingDate: [],
    demandType: false,
    districtType: null,
    personTotal: null,
    miceType: null,
    intentionConsultantUserCode: void 0,
  };
};

const changeConsultantType = () => {
  consultantId.value = '';
};

const consultantAnswer = `
会议顾问的核心目标是让会议更高效、更有成果、更省时省力。它可以贯穿会议的整个生命周期（会前、会中、会后），为您提供全方位的支持，具体能做以下这些事情：
    1. 全流程协助
    2. 会议互动提报
    3. 账单审核
    4. 现场服务（视会议大小决定）
`
</script>

<template>
  <a-modal :width="1056" title="选择您的专属会议顾问" v-model:open="modelOpen" class="home-consultant-modal" centered>
    <div class="consultant-content">
      <h-radio-group class="consultant-type" v-model:value="consultantType" @change="changeConsultantType">
        <h-radio class="consultant-type-item" value="0">随机分配最优秀顾问</h-radio>
        <h-radio class="consultant-type-item" value="1">我有倾向的会议顾问，自己选择（根据顾问排期，并非一定可分配）
        </h-radio>
      </h-radio-group>
      <div class="consultant-list">
        <div v-for="consultantItem of store.consultantList" class="consultant-item" :key="consultantItem.username">
          <img :src="consultantItem.pic"/>
          <!-- <div v-if="!consultantItem.scheduling" class="consultant-tag flex-center">排期已满</div> -->
          <div class="consultant-check" @click="consultantId = consultantItem.username">
            <div v-show="consultantType === '0'" class="consultant-check-disabled"></div>
            <div
                v-show="consultantType === '1' && consultantId === consultantItem.username"
                class="consultant-check-checked"
            >
              <img :src="CheckCircleFilledImg"/>
            </div>
            <div
                v-show="consultantType === '1' && consultantId !== consultantItem.username"
                class="consultant-check-unchecked"
            />
          </div>
          <div class="consultant-info">
            <div class="consultant-name text-ellipsis">{{ consultantItem.nickName }}</div>
            <div class="consultant-desc text-ellipsis">{{ consultantItem.description }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="consultant-summary">
      <div class="consultant-question">会议顾问能为您做什么？</div>
      <div class="consultant-answer">
        {{ consultantAnswer }}
      </div>
    </div>
    <template #footer>
      <div class="flex-center">
        <h-button type="primary" :loading="loading" @click="next" :style="{ width: '88px', borderRadius: '4px' }"
        >下一步
        </h-button
        >
      </div>
    </template>
  </a-modal>

  <a-modal v-model:open="adviserOpen" title="顾问代提">
    <p>提交成功！会议顾问稍候与您联系！</p>

    <template #footer>
      <div class="flex-center">
        <h-button type="primary" @click="handleOk" :style="{ width: '88px', borderRadius: '4px' }">确定</h-button>
      </div>
    </template>
  </a-modal>
</template>

<style lang="less">
.home-consultant-modal {
  .ant-modal-content {
    background: linear-gradient(181deg, #e4efff 0%, #ffffff 200px, #ffffff 100%) !important;
    border-radius: 16px !important;
    padding: 15px 64px;

    .ant-modal-header {
      background: transparent;

      .ant-modal-title {
        text-align: center;
        font-weight: 500;
        font-size: 24px;
        color: #1d2129;
      }
    }
  }

  .ant-modal-footer {
    /* margin-top: 28px; */
    margin-top: 12px;
  }

  .ant-radio {
    .ant-radio-inner {
      background-color: #fff;

      &::after {
        transform: scale(0.5);
        background-color: #1677ff;
      }
    }
  }

  .consultant-content {
    padding: 0 16px;

    .meeting-date {
      color: #86909c;
      line-height: 20px;

      .meeting-date-divider {
        font-weight: 500;
        font-size: 20px;
        color: #1d2129;
        margin: 0 8px;
      }
    }

    .consultant-type {
      /* margin-top: 20px; */
      margin-top: 10px;

      .consultant-type-item {
        width: 100%;
        /* margin-top: 10px; */
        margin-top: 6px;
      }
    }

    .consultant-list {
      /* margin-top: 16px; */
      margin-top: 8px;
      display: flex;
      width: 100%;
      overflow-x: auto;
      padding-bottom: 2px;

      .consultant-item {
        flex: 0 0 200px;
        position: relative;
        margin-right: 32px;
        height: 250px;

        &:last-child {
          margin-right: 0;
        }

        > img {
          width: 100%;
          height: 100%;
        }

        .consultant-tag {
          position: absolute;
          top: 0;
          left: 0;
          width: 68px;
          height: 24px;
          background: #fff1f0;
          border-radius: 0px 0px 12px 0px;
          font-weight: 500;
          font-size: 12px;
          color: #ff5533;
        }

        .consultant-check {
          cursor: pointer;
          position: absolute;
          top: 10px;
          right: 12px;

          .consultant-check-disabled,
          .consultant-check-unchecked {
            width: 20px;
            height: 20px;
            background: #fff;
            border-radius: 10px;
            border: 1px solid #bfbfbf;
          }

          .consultant-check-disabled {
            background: #e5e6e8;
            cursor: not-allowed;
          }

          .consultant-check-checked {
            width: 20px;
            height: 20px;

            img {
              width: 100%;
              height: 100%;
              vertical-align: text-top;
            }
          }
        }

        .consultant-info {
          position: absolute;
          bottom: 18px;
          left: 20px;
          max-width: calc(100% - 30px);

          .consultant-name {
            font-weight: 500;
            font-size: 18px;
            color: #ffffff;
          }

          .consultant-desc {
            font-size: 16px;
            color: #ffffff;
          }
        }
      }
    }
  }

  .consultant-summary {
    /* margin-top: 20px; */
    margin-top: 10px;
    width: 100%;
    padding: 16px;
    background: #fbfdff;
    border-radius: 12px;
    border: 1px solid rgba(24, 104, 219, 0.2);

    .consultant-question {
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 22px;
    }

    .consultant-answer {
      white-space: pre-wrap;
      word-break: break-word;
      display: inline-block;
      margin-top: 6px;
      font-size: 14px;
      color: #4e5969;
      line-height: 25px;
    }
  }
}
</style>
