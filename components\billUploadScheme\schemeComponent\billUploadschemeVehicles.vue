<script setup lang="ts">
// 方案互动-用车方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  SeatTypeConstant,
  CarBrandTypeConstant,
  CarUsageTypeConstant,
  UsageTimeTypeConstant,
  HotelsArr,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  readonly: {
    // 是否为只读模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeVehiclesEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every(
    (e) => e.billUnitPrice && e.billVehicleNum && e.billUnitPrice >= 0 && e.billVehicleNum >= 0,
  );
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.billUnitPrice * e.billVehicleNum;
    });

    emit('schemePriceEmit', { type: 'vehicle', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

watch(
  () => props.schemeItem,
  (newObj) => {
    // 左侧显示原始方案数据（从schemeItem获取，因为已经按日期分组了）
    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.vehicles || [];

    if (props.isSchemeCache && props.schemeCacheItem) {
      // 缓存 - 反显
      const cacheVehicles = props.schemeCacheItem?.vehicles || [];

      if (cacheVehicles.length === 0) {
        const demandData = JSON.parse(JSON.stringify(newObj))?.vehicles || [];

        newSchemeList.value = demandData.map((e: any) => {
          // 从原始需求数据中获取对应的方案信息（用于左侧显示）
          const originalDemandItem =
            props.demandInfo?.vehicles?.find(
              (demandItem: any) =>
                demandItem.miceDemandVehicleId === e.miceDemandVehicleId || demandItem.id === e.miceDemandVehicleId,
            ) || {};

          return {
            // 基础字段
            miceDemandVehicleId: e.miceDemandVehicleId,
            miceSchemeVehicleId: e.id, // 方案车辆id - 使用详情中的id

            // 日期和基本信息
            demandDate: e.demandDate,
            usageType: e.usageType,
            usageTime: e.usageTime,
            seats: e.seats,

            // 车辆数量 - 正确映射
            schemeVehicleNum: originalDemandItem.schemeVehicleNum, // 从demandInfo获取方案车辆数（左侧显示，不可修改）
            billVehicleNum: e.schemeVehicleNum, // 当前数据中的schemeVehicleNum实际是账单车辆数

            // 车辆信息
            brand: e.brand,
            route: e.route,
            description: e.description,

            // 价格相关 - 正确映射
            schemeUnitPrice: originalDemandItem.schemeUnitPrice, // 从demandInfo获取方案单价（左侧显示，不可修改）
            billUnitPrice: undefined, // 初次进入不回显，让用户手动输入

            // 其他字段
            sourceId: e.sourceId,
            invoiceTempId: undefined, // 临时id，用于关联发票表
            statementTempId: undefined, // 临时id，用于关联水单表
          };
        });
      } else {
        // 修正缓存数据中的字段映射
        newSchemeList.value = cacheVehicles.map((cacheItem: any) => {
          // 从原始需求数据中获取对应的方案信息（用于左侧显示）
          const originalDemandItem =
            props.demandInfo?.vehicles?.find(
              (demandItem: any) =>
                demandItem.miceDemandVehicleId === cacheItem.miceDemandVehicleId ||
                demandItem.id === cacheItem.miceDemandVehicleId,
            ) || {};

          // 从详情数据中获取对应的方案车辆信息（用于获取ID和sourceId）
          const originalSchemeItem =
            oldSchemeList.value.find(
              (schemeItem: any) =>
                schemeItem.miceDemandVehicleId === cacheItem.miceDemandVehicleId ||
                schemeItem.id === cacheItem.miceSchemeVehicleId,
            ) || {};

          return {
            ...cacheItem,
            // 修正字段映射
            schemeVehicleNum: originalDemandItem.schemeVehicleNum, // 从demandInfo获取方案车辆数（左侧显示，不可修改）
            schemeUnitPrice: originalDemandItem.schemeUnitPrice, // 从demandInfo获取方案单价（左侧显示，不可修改）
            billVehicleNum: cacheItem.schemeVehicleNum, // 缓存中的schemeVehicleNum实际是账单车辆数
            billUnitPrice: cacheItem.schemeUnitPrice, // 缓存中的schemeUnitPrice实际是账单单价

            // 🔧 从详情数据中补充缺失的ID字段
            miceSchemeVehicleId: originalSchemeItem.id || cacheItem.miceSchemeVehicleId, // 方案车辆ID
            sourceId: originalSchemeItem.sourceId || cacheItem.sourceId, // 来源ID
          };
        });
      }

      // 价格计算
      priceCalcFun();
    } else {
      const demandData = JSON.parse(JSON.stringify(newObj))?.vehicles || [];

      newSchemeList.value = demandData.map((e: any) => {
        // 从原始需求数据中获取对应的方案信息（用于左侧显示）
        const originalDemandItem =
          props.demandInfo?.vehicles?.find(
            (demandItem: any) =>
              demandItem.miceDemandVehicleId === e.miceDemandVehicleId || demandItem.id === e.miceDemandVehicleId,
          ) || {};

        return {
          // 基础字段
          miceDemandVehicleId: e.miceDemandVehicleId,
          miceSchemeVehicleId: e.id, // 方案车辆id - 使用详情中的id

          // 日期和基本信息
          demandDate: e.demandDate,
          usageType: e.usageType,
          usageTime: e.usageTime,
          seats: e.seats,

          // 车辆数量 - 正确映射
          schemeVehicleNum: originalDemandItem.schemeVehicleNum, // 从demandInfo获取方案车辆数（左侧显示，不可修改）
          billVehicleNum: e.schemeVehicleNum, // 当前数据中的schemeVehicleNum实际是账单车辆数

          // 车辆信息
          brand: e.brand,
          route: e.route,
          description: e.description,

          // 价格相关 - 正确映射
          schemeUnitPrice: originalDemandItem.schemeUnitPrice, // 从demandInfo获取方案单价（左侧显示，不可修改）
          billUnitPrice: props.readonly ? e.billUnitPrice || null : null, // 只读模式使用已有数据，编辑模式为空

          // 其他字段
          sourceId: e.sourceId,
          invoiceTempId: undefined, // 临时id，用于关联发票表
          statementTempId: undefined, // 临时id，用于关联水单表
        };
      });
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['用车方式', '车型', '型号', '用车数量', '使用时长', '路线', '路线概述', '备注'];

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const vehicleTempSave = () => {
  emit('schemeVehiclesEmit', {
    schemeVehicles: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const vehicleSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (!e.billUnitPrice) {
      message.error('请输入' + e.demandDate + '用车' + (i + 1) + '账单单价');

      isVerPassed = false;
      anchorJump('schemeVehicleId' + e.demandDate + i);
      return;
    }
  });

  if (isVerPassed) {
    vehicleTempSave();
  }

  return isVerPassed;
};

defineExpose({ vehicleSub, vehicleTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 用车方案 -->
  <div class="scheme_vehicle">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>用车方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用车' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
                </template>
                {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
                </template>
                {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
                </template>
                {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.schemeVehicleNum ? item.schemeVehicleNum + '辆' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
                </template>
                {{ item.usageType == 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 0 ? item.route || '-' : '-' }}
                </template>
                <!-- 单趟 -->
                {{ item.usageType == 0 ? item.route || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 1 ? item.route || '-' : '-' }}
                </template>
                <!-- 包车 -->
                {{ item.usageType == 1 ? item.route || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>

          <!-- 左侧价格显示区域 - 只读 -->
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeUnitPrice ? '¥' + formatNumberThousands(item.schemeUnitPrice) : '-' }}
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemeVehicleNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemeVehicleNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemeVehicleNum">
                {{ item.schemeVehicleNum + '辆*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>用车账单</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用车' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
                </template>
                {{ CarUsageTypeConstant.ofType(item.usageType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
                </template>
                {{ SeatTypeConstant.ofType(item.seats)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
                </template>
                {{ CarBrandTypeConstant.ofType(item.brand)?.desc || '-' }}
              </a-tooltip>
            </div>

            <div class="scheme_plan_value p0">
              <div class="pl12" v-if="props.readonly || props.schemeType === 'biddingView' || props.schemeType === 'schemeView'">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.billVehicleNum || item.schemeVehicleNum ? (item.billVehicleNum || item.schemeVehicleNum) + '辆' : '-' }}
                  </template>
                  {{ item.billVehicleNum || item.schemeVehicleNum ? (item.billVehicleNum || item.schemeVehicleNum) + '辆' : '-' }}
                </a-tooltip>
              </div>
              <div class="pl12" v-else>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.billVehicleNum">
                    {{ item.billVehicleNum ? item.billVehicleNum + '辆' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.billVehicleNum"
                    @change="changePrice(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="账单车辆数"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType === 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
                </template>
                {{ item.usageType === 1 ? UsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType === 0 ? item.route || '-' : '-' }}
                </template>
                {{ item.usageType === 0 ? item.route || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.usageType == 1 ? item.route || '-' : '-' }}
                </template>
                {{ item.usageType == 1 ? item.route || '-' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value p0">
              <div class="pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemeVehicleId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>

              <div
                class="scheme_plan_price_value"
                v-if="props.readonly || props.schemeType === 'biddingView' || props.schemeType === 'schemeView'"
              >
                {{
                  item.billUnitPrice || item.billUnitPrice ? '¥' + formatNumberThousands(item.billUnitPrice) : '-'
                }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.billUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.billUnitPrice"
                  @change="changePrice(idx)"
                  placeholder="请输入账单单价"
                  :bordered="false"
                  :controls="false"
                  :min="0.01"
                  :max="999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.billUnitPrice && item.billVehicleNum
                    ? formatNumberThousands(item.billUnitPrice * item.billVehicleNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.billUnitPrice">
                {{ item.billVehicleNum + '辆*' + item.billUnitPrice + '(账单单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_vehicle.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  // 车辆数量输入框样式，模仿备注字段
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }
  }

  .scheme_plan_value {
    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
