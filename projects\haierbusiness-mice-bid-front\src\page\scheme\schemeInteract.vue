<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import schemeInteract from '@haierbusiness-front/components/scheme/schemeInteract.vue';
import { meetingProcessOrchestration, resolveParam } from '@haierbusiness-front/utils';
import { Button, Dropdown, Menu, Pagination, BadgeRibbon, Tooltip, Popover, message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
const route = useRoute();
const frameModel = ref(inject<any>('frameModel'));
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
});
const presentConfirm = async () => {
  Modal.confirm({
    title: '礼品确认',
    content: '下单礼品是否确认？',
    async onOk() {
      const res = await miceBidManOrderListApi.infoConfirm({
        mainCode: routeQuery.record.mainCode,
        confirmStatus: 1,
        rejectReason: '',
      });
      console.log(res);
      if (res.success) {
        message.success('确认成功！');
        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
        // 跳转需求确认页面
        const url =
          (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
          '/card-order/miceOrder';
        window.location.href = url;
      }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
const open = ref(true);
const reason = ref<string>(''); // 驳回原因

const cancelConfirm = async () => {
  if (reason.value === '') {
    message.error('驳回原因不能为空！');
    return;
  }
  const res = await miceBidManOrderListApi.infoConfirm({
    mainCode: routeQuery.record.mainCode,
    confirmStatus: 3,
    rejectReason: reason.value,
  });
  console.log(res);
  if (res.success) {
    message.success('驳回成功！');
    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
    // 跳转需求确认页面
    const url =
      (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
      '/card-order/miceOrder';
    window.location.href = url;
  }
};
onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0;
  console.log('frameModel.value', frameModel.value);
});
</script>

<template>
  <div class="container">
    <!-- 方案互动 -->
    <schemeInteract />
    <div class="footer">
      <a-button style="margin-right: 20px" type="primary" danger @click="open = true">驳回</a-button>
      <a-button type="primary" @click="presentConfirm">确认</a-button>
      <a-modal v-model:open="open" title="驳回原因" @ok="cancelConfirm">
        <a-textarea v-model:value="reason" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
      </a-modal>
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  padding-bottom: 40px;
}
.footer {
  border-top: 1px solid #ccc;
  right: 0;
  background: #fff;
  z-index: 11;
  width: 100%;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  text-align: right;
}
</style>
