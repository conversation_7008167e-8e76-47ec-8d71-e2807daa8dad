<script setup lang="ts">
import {
  BreadcrumbItem as hBreadcrumbItem,
  Breadcrumb as hBreadcrumb,
  LayoutSider as hLayoutSider,
  LayoutFooter as hLayoutFooter,
  LayoutContent as hLayoutContent,
  LayoutHeader as hLayoutHeader,
  Layout as hLayout,
  MenuItem as hMenuItem,
  MenuItemGroup as hMenuItemGroup,
  SubMenu as hSubMenu,
  Menu as hMenu,
  Divider as hDivider,
  Space as hSpace,
  Button as hButton,
  Col as hCol,
  Result as hResult,
  Row as hRow,
  TabPane as hTabPane,
  Tabs as hTabs,
  message,
} from 'ant-design-vue';
import { onBeforeMount, onBeforeUnmount, onMounted, onUnmounted, ref } from 'vue';
import {
  UploadOutlined,
  UserOutlined,
  NotificationOutlined,
  AppstoreOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons-vue';
import EManage from '@haierbusiness-front/components/manange/EManange.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
//通知弹框逻辑
import notice from './component/Modal.vue';

const store = applicationStore();
const { resource } = storeToRefs(store);
//通知弹框逻辑
const noticeVisible = ref(false)
//
const handleShow = () => {
  noticeVisible.value = false
  console.log(noticeVisible.value);
}
const handlecancel = () => {
  noticeVisible.value = false
}

onMounted(() => {
  noticeVisible.value = true
  // try {
  //   const showValue = sessionStorage.getItem('Show')
  //   if (showValue === null || showValue === undefined) {
  //     console.log('走着一步');
  //     noticeVisible.value = true
  //     if(noticeVisible.value){
  //       sessionStorage.setItem('Show', JSON.stringify(false))
  //     }
  //   } else {
  //     noticeVisible.value = JSON.parse(showValue)
  //   }
  // } catch (e) {
  //   console.error('解析sessionStorage失败', e)
  //   noticeVisible.value = false
  // }
  // console.log(noticeVisible.value,"noticeVisible.value");
  
})

// 根据实际需求决定是否保留这一行
onUnmounted(() => {
  sessionStorage.removeItem('Show')
})

</script>

<template>
  <div style="height: 100vh; min-height: 280px">
    <e-manage :param="resource"></e-manage>
  </div>
  <!-- 通知弹框逻辑 -->
  <notice v-model="noticeVisible" @ok="handleShow" @cancel="handlecancel" style="width: 500px;"></notice>
</template>

<style scoped lang="less">
:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>
