<script setup lang="ts">
// 方案变更-拓展方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

import { ActivitiesArr } from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  processNode: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeActivityEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 拓展活动
const activitiesParams = ref<ActivitiesArr>({
  demandDate: '', // 需求日期
  demandUnitPrice: null, // 费用标准
  schemePersonNum: null, // 人数
  desc: null, // 活动说明
  fileList: [], // 上传文件
  paths: [],

  schemeUnitPrice: null, // 自动测算单价

  description: '',
});

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every(
    (e) => e.schemeUnitPrice && e.schemeUnitPrice > 0 && e.schemePersonNum && e.schemeUnitPrice > 0,
  );
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      if (e.schemeUnitPrice && e.schemePersonNum) {
        subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
      }
    });

    emit('schemePriceEmit', { type: 'activity', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

const schemePlanLabelList = ['费用标准', '人数', '活动需求', '附件'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemePersonNum;
  }

  // 价格计算
  priceCalcFun();
};

const fileChange = (fileList: array) => {
  let document = '';
  let file = {};

  fileList.forEach((item, index) => {
    let isJson = true;

    try {
      file = JSON.parse(item);
    } catch (error) {
      console.log(error);
      isJson = false;
    }

    if (!isJson) return;

    document += `
      <a target='_blank' href='${file.url}'>${file.name}</a>
      <span style='margin-right: 10px;color: #86909c' >${index === fileList.length - 1 ? '' : ','}</span>
    `;
  });

  return document ? document : '-';
};

const addScheme = (idx: number) => {
  isVerifyFailed.value = false;

  newSchemeList.value.push({
    isSchemeChangeAdd: true, // 是否变更方案新增

    ...activitiesParams.value,
    tempId: Date.now() + idx,

    demandDate: oldSchemeList.value[0]?.demandDate || '',
  });

  // priceCalcFun();
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  // priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const activityTempSave = () => {
  emit('schemeActivityEmit', {
    schemeActivities: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const activitySub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (e.schemeUnitPrice === null || e.schemeUnitPrice === undefined) {
      message.error('请填写' + e.demandDate + '拓展方案' + (i + 1) + '费用标准');

      isVerPassed = false;
      anchorJump('schemeActivitiesId' + e.demandDate + i);
      return;
    }

    if (e.schemePersonNum === null || e.schemePersonNum === undefined) {
      message.error('请填写' + e.demandDate + '拓展方案' + (i + 1) + '人数');

      isVerPassed = false;
      anchorJump('schemeActivitiesId' + e.demandDate + i);
      return;
    }

    if (!e.description) {
      message.error('请填写' + e.demandDate + '拓展方案' + (i + 1) + '活动需求');

      isVerPassed = false;
      anchorJump('schemeActivitiesId' + e.demandDate + i);
      return;
    }
  });

  if (isVerPassed) {
    activityTempSave();
  }

  return isVerPassed;
};

defineExpose({ activitySub, activityTempSave });

onMounted(async () => {
  if (
    (props.schemeItem && props.schemeItem.activities) ||
    (props.schemeCacheItem && props.schemeCacheItem.activities)
  ) {
    // console.log('%c [ 拓展活动 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeItem.activities);

    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.activities || [];

    if (props.isSchemeCache && props.schemeCacheItem) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheItem?.activities || [];
    } else {
      // 议价、议价查看、 变更查看
      newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));
    }

    // 价格计算
    priceCalcFun();
  }
});
</script>

<template>
  <!-- 拓展方案 -->
  <div class="scheme_vehicle" v-if="oldSchemeList.length > 0 || newSchemeList.length > 0">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_title" v-show="oldSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>拓展方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '拓展方案' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </template>
                {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12" v-html="fileChange(item.paths)"></div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title" v-show="newSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>拓展方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '拓展方案' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeUnitPrice === null || item.schemeUnitPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                    </template>
                    {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemeUnitPrice"
                    @blur="changePrice(idx)"
                    placeholder="请填写费用标准"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="99999.99"
                    :precision="2"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemePersonNum === null || item.schemePersonNum === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                    </template>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemePersonNum"
                    @blur="changePrice(idx)"
                    placeholder="请填写人数"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span>人</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.description === null || item.description === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.description || '-' }}
                    </template>
                    {{ item.description || '-' }}
                  </a-tooltip>
                </div>

                <div v-else>
                  <a-input
                    v-model:value="item.description"
                    style="width: calc(100% - 30px)"
                    placeholder="请填写活动需求"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12" v-html="fileChange(item.paths)"></div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemeActivitiesId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>

            <!-- 操作 -->
            <div
              class="action_icons"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                schemeChangeType === 'schemeChangeEdit' &&
                newSchemeList.length > 1 &&
                item.isSchemeChangeAdd
              "
            >
              <a-popconfirm
                :title="'确认删除拓展方案' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div
          v-if="
            ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
            schemeChangeType === 'schemeChangeEdit'
          "
          class="add_scheme_plan mt20"
          @click="addScheme(index)"
        >
          <div class="plan_add_img mr8"></div>
          <span>新增拓展方案</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_activity.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }

  .pr0 {
    padding-right: 0 !important;
  }
}
</style>
